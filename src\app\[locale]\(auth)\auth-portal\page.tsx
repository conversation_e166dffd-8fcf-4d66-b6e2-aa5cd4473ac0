import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Building2, GalleryVerticalEnd, Users } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';

export default async function AuthPortalPage() {
  const t = await getTranslations('authPortal');

  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-2 font-medium">
            <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-4" />
            </div>
            SimPLE
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-md space-y-6">
            <div className="text-center">
              <h1 className="text-3xl font-bold">{t('title')}</h1>
              <p className="text-muted-foreground mt-2">{t('subtitle')}</p>
            </div>

            <div className="grid gap-4">
              {/* Admin Portal */}
              <Card className="cursor-pointer hover:bg-accent/50 transition-colors">
                <CardHeader className="text-center">
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                    <Building2 className="w-6 h-6 text-blue-600" />
                  </div>
                  <CardTitle>{t('adminPortalTitle')}</CardTitle>
                  <CardDescription>
                    {t('adminPortalDescription')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <Button asChild variant="default" className="w-full">
                      <Link href="/admin/login">{t('adminLogin')}</Link>
                    </Button>
                    <Button asChild variant="outline" className="w-full">
                      <Link href="/admin/register">{t('adminRegister')}</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Contractor Portal */}
              <Card className="cursor-pointer hover:bg-accent/50 transition-colors">
                <CardHeader className="text-center">
                  <div className="mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-2">
                    <Users className="w-6 h-6 text-green-600" />
                  </div>
                  <CardTitle>{t('contractorPortalTitle')}</CardTitle>
                  <CardDescription>
                    {t('contractorPortalDescription')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <Button asChild variant="default" className="w-full">
                      <Link href="/contractor/login">
                        {t('contractorLogin')}
                      </Link>
                    </Button>
                    <Button asChild variant="outline" className="w-full">
                      <Link href="/contractor/register">
                        {t('contractorRegister')}
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="text-center text-sm text-muted-foreground">
              <p>{t('helpText')}</p>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-muted relative hidden lg:block">
        <Image
          src="https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
          alt="Professional office building and construction site"
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
        <div className="absolute bottom-6 left-6 text-white">
          <h2 className="text-2xl font-bold mb-2">{t('heroTitle')}</h2>
          <p className="text-lg opacity-90">{t('heroSubtitle')}</p>
        </div>
      </div>
    </div>
  );
}
