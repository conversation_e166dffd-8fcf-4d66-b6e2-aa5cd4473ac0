{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "submit": "Submit", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "close": "Close", "yes": "Yes", "no": "No", "notSet": "Not set", "unknown": "Unknown", "firstTime": "First time", "justNow": "Just now", "new": "New", "viewDetails": "View Details"}, "navigation": {"navigation": "Navigation", "professionalEdition": "Professional Edition", "signingOut": "Signing out...", "signOut": "Sign Out", "loadingMenu": "Loading menu...", "languages": {"en": "English", "ms": "Bahasa Malaysia"}, "dashboard": "Dashboard", "projects": "Projects", "lifts": "Lifts", "buildings": "Buildings", "contracts": "Contracts", "dailyLogs": "Daily Logs", "pmas": "PMAs", "complaints": "<PERSON><PERSON><PERSON><PERSON>", "contractors": "Contractors", "clients": "Clients", "blacklist": "Blacklist", "analytics": "Analytics", "reports": "Reports", "users": "Users", "profile": "Profile", "settings": "Settings", "maintenanceLogs": "Maintenance Logs", "members": "Members", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "descriptions": {"dashboard": "Overview of lift management activities", "projects": "Manage your assigned projects and tasks", "lifts": "Manage lift inventory and status", "buildings": "Manage building information", "contracts": "Maintenance contracts management", "dailyLogs": "Daily monitoring and maintenance logs", "pmas": "Periodic Maintenance Agreements", "complaints": "Customer complaints and issues", "contractors": "Manage contractor registrations and certifications", "clients": "Manage client information", "blacklist": "Blacklisted contractors management", "analytics": "Performance analytics and insights", "reports": "Generate and view system reports", "users": "System user management", "profile": "Manage your profile settings", "settings": "System configuration settings"}}, "dashboard": {"title": "Dashboard", "welcomeBack": "Welcome back, {name}!", "errorLoading": "Error loading profile: {error}", "profile": {"title": "Your Profile", "email": "Email", "name": "Name", "role": "Role", "phone": "Phone", "memberSince": "Member Since", "lastLogin": "Last Login"}, "quickActions": {"title": "Quick Actions", "editProfile": "Edit Profile", "settings": "Settings"}, "recentActivity": {"title": "Recent Activity", "noActivity": "No recent activity to display."}, "statistics": {"title": "Account Statistics", "loginStatus": "Login Status", "accountType": "Account Type", "lastLogin": "Last Login", "active": "Active", "standard": "Standard"}}, "pages": {"projects": {"title": "Projects", "description": "Manage your assigned projects and tasks", "noProjects": "No Projects Found", "noProjectsDescription": "You don't have any assigned projects yet.", "createFirst": "Create Your First Project", "stats": {"total": "Total Projects", "active": "Active", "pending": "Pending", "completed": "Completed"}, "create": {"title": "Create New Project", "description": "Fill in the details below to create a new project", "form": {"title": "Project Information", "description": "Enter the basic information about your project", "fields": {"name": {"label": "Project Name", "placeholder": "Enter project name"}, "code": {"label": "Quotation Number", "placeholder": "e.g., QUO-2025-001"}, "location": {"label": "Location", "placeholder": "Enter project location"}, "startDate": {"label": "Start Date"}, "endDate": {"label": "End Date"}, "status": {"label": "Status", "placeholder": "Select project status", "options": {"pending": "Pending", "active": "Active", "completed": "Completed", "cancelled": "Cancelled"}}, "description": {"label": "Description", "placeholder": "Enter project description (optional)"}}, "actions": {"create": "Create Project", "creating": "Creating..."}}, "validation": {"required": "Required fields missing", "requiredFields": "Please fill in all required fields", "dateError": "Invalid date range", "endDateAfterStart": "End date must be after start date"}, "success": {"title": "Project created successfully", "description": "Your new project has been created and is ready to use"}, "error": {"title": "Failed to create project", "description": "There was an error creating the project. Please try again"}}}}, "auth": {"loginTitle": "Login to your account", "loginSubtitle": "Enter your email below to login to your account", "registerTitle": "Create your account", "registerSubtitle": "Enter your information below to create your account", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "phoneNumber": "Phone Number", "role": "Role", "emailPlaceholder": "<EMAIL>", "fullNamePlaceholder": "<PERSON>", "phonePlaceholder": "+6********** or **********", "forgotPassword": "Forgot your password?", "selectRole": "Select your role", "jkr": "JKR (Jabatan Kerja Raya)", "jkrPic": "JKR PIC (Person in Charge)", "jkrAdmin": "JKR Admin", "admin": "Administrator", "contractor": "Contractor", "viewer": "Viewer", "client": "Client", "login": "<PERSON><PERSON>", "signUp": "Sign up", "signIn": "Sign in", "createAccount": "Create Account", "signingIn": "Signing in...", "creatingAccount": "Creating Account...", "noAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "phoneTooltip1": "Malaysian phone numbers only", "phoneTooltip2": "e.g., +6**********, **********", "passwordRequirements": "Password must contain:", "passwordReq1": "At least 8 characters", "passwordReq2": "One uppercase letter (A-Z)", "passwordReq3": "One lowercase letter (a-z)", "passwordReq4": "One number (0-9)", "passwordReq5": "One special character (@$!%*?&)", "loginSuccess": "Successfully signed in! Redirecting to dashboard...", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "registerSuccess": "Account created successfully! Please check your email to verify your account.", "registerFailed": "Failed to create account. Please try again.", "userExists": "An account with this email already exists. Please try logging in instead.", "profileCreationError": "Registration failed during profile creation. Please try again or contact support.", "signupDisabled": "Account registration is currently disabled. Please contact support.", "adminLoginTitle": "Admin Portal Login", "adminLoginSubtitle": "Access the administrative dashboard", "adminRegisterTitle": "Admin Registration", "adminRegisterSubtitle": "Create your administrator account", "adminLogin": "<PERSON><PERSON>", "adminSignUp": "Admin Sign up", "adminSignIn": "Admin Sign in", "createAdminAccount": "Create Ad<PERSON> Account", "creatingAdminAccount": "Creating <PERSON><PERSON> Account...", "noAdminAccount": "Don't have an admin account?", "alreadyHaveAdminAccount": "Already have an admin account?", "adminLoginSuccess": "Admin login successful! Redirecting to dashboard...", "adminRegisterSuccess": "Admin account created successfully! Please check your email to verify your account.", "contractorLoginTitle": "Contractor <PERSON>", "contractorLoginSubtitle": "Access your contractor dashboard", "contractorRegisterTitle": "Contractor Registration", "contractorRegisterSubtitle": "Join our contractor network", "contractorLogin": "Contractor <PERSON><PERSON>", "contractorSignUp": "Contractor Sign up", "contractorSignIn": "Contractor Sign in", "createContractorAccount": "Create Contractor Account", "creatingContractorAccount": "Creating Contractor Account...", "noContractorAccount": "Don't have a contractor account?", "alreadyHaveContractorAccount": "Already have a contractor account?", "contractorLoginSuccess": "Contractor login successful! Redirecting to dashboard...", "contractorRegisterSuccess": "Contractor account created successfully! Please check your email to verify your account.", "contractorNote": "Note: Additional company information will be required during onboarding.", "contractorNoteSubtext": "You'll provide company details, certifications, and project capabilities after registration.", "forgotPasswordTitle": "Reset your password", "forgotPasswordSubtitle": "Enter your email below and we'll send you a reset link", "sendResetLink": "Send Reset Link", "sendingResetLink": "Sending reset link...", "resetLinkSent": "Check your email for the reset link", "resetLinkFailed": "Failed to send reset link. Please try again.", "backToLogin": "Back to login", "verifyCodeTitle": "Enter verification code", "verifyCodeSubtitle": "Enter the 6-digit code sent to your email", "verificationCode": "Verification Code", "codePlaceholder": "Enter 6-digit code", "verifyCode": "Verify Code", "verifyingCode": "Verifying code...", "codeVerified": "Code verified successfully! Redirecting...", "codeVerificationFailed": "Invalid or expired code. Please try again.", "didNotReceiveCode": "Didn't receive the code?", "resendCode": "Resend code", "alreadyHaveCode": "Already have a verification code?", "resetPasswordTitle": "Set your new password", "resetPasswordSubtitle": "Choose a strong password to secure your account", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "newPasswordPlaceholder": "Enter your new password", "confirmPasswordPlaceholder": "Confirm your new password", "updatePassword": "Update Password", "updatingPassword": "Updating password...", "passwordUpdated": "Password updated successfully! Please login with your new password.", "passwordUpdateFailed": "Failed to update password. Please try again."}, "contractor": {"onboarding": {"title": "Contractor Onboarding", "step1": {"title": "Personal Information", "fullName": "Full Name", "fullNamePlaceholder": "Enter your full name", "icNumber": "IC Number", "icNumberPlaceholder": "123456-78-9012", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "01X-XXXXXXX", "role": "Role", "rolePlaceholder": "Select your role", "roleHelp": "Select your role in the company", "technician": "Technician", "admin": "Administrator", "cp": "Competent Person (CP)", "nextButton": "Next: Role Information"}, "step2": {"title": "Role Information - {role}", "nextButton": "Next: Company Setup", "cp": {"title": "Competent Person Information", "name": "Name", "namePlaceholder": "Enter competent person name", "category": "Category", "categoryPlaceholder": "Select CP category", "categoryA": "Category A", "categoryB": "Category B", "categoryC": "Category C", "icNo": "IC Number", "icNoPlaceholder": "123456-78-9012", "cpNumber": "CP Number", "cpNumberPlaceholder": "Enter CP registration number", "tel": "Telephone", "telPlaceholder": "01X-XXXXXXX", "email": "Email", "emailPlaceholder": "<EMAIL>", "liftListFiles": "List of Lift Files", "liftFilesPlateholder": "Upload lift list files (PDF, DOC, DOCX, XLS, XLSX)", "noLiftFiles": "No lift list files uploaded yet", "registrationCertFile": "Registration Certificate File", "registrationCertFilePlaceholder": "Upload registration certificate (PDF, DOC, DOCX, JPG, PNG)", "registrationCertFileHelp": "Upload your CP registration certificate file (minimum 10MB)"}, "admin": {"title": "Administrator Information", "name": "Name", "namePlaceholder": "Enter administrator name", "nameHelp": "This will be used for administrative identification"}, "technician": {"title": "Technician Information", "name": "Name", "namePlaceholder": "Enter technician name", "nameHelp": "This will be used for technician identification"}}, "step3": {"title": "Company Setup", "createTitle": "Create New Company", "createDescription": "Register a new company that hasn't been registered in the system yet", "joinTitle": "Join Existing Company", "joinDescription": "Join a company that is already registered in the system", "specialCode": "Company Special Code", "specialCodePlaceholder": "Enter the special code provided by the company", "specialCodeHelp": "Enter the special code provided by your company administrator", "continueToCreation": "Continue to Company Creation", "joinCompanyButton": "Join Company"}, "step4": {"title": "Company Creation", "companyCode": "Company Code", "companyCodeHelp": "This unique code will identify your company in the system", "companyName": "Company Name", "companyNamePlaceholder": "Enter your company name", "companyNameHelp": "Company name will be converted to uppercase and must be unique.", "companyNamePreview": "Preview: {name}", "companyType": "Company Type", "companyTypeHelp": "Select your company type", "companyHotline": "Company Hotline", "companyHotlinePlaceholder": "Enter company hotline number", "oemName": "OEM Name", "oemNamePlaceholder": "Enter OEM name", "oemNameHelp": "Required for OEM type companies", "createButton": "Create Company", "creatingButton": "Creating Company..."}, "progress": {"step": "Step {current} of {total}", "personalInfo": "Personal Info", "companyInfo": "Company Info", "finalDetails": "Final Details"}}}, "company": {"types": {"sdn_bhd": "Sdn Bhd", "bhd": "Bhd", "partnership": "Partnership", "sole_proprietorship": "Sole Proprietorship", "llp": "LLP", "competent_firm": "Competent Firm", "non_competent_firm": "Non-Competent Firm", "oem": "OEM (Original Equipment Manufacturer)"}, "availability": {"checking": "Checking availability...", "available": "Company name is available", "unavailable": "Company name is already taken", "error": "Error checking availability"}}, "agencies": {"JKR": "Jabatan Kerja Raya Malaysia (JKR)", "KKM": "Kementerian Kesihatan Malaysia (KKM)", "KPM": "Kementerian Pendidikan Malaysia (KPM)", "KPKT": "Kementerian Perumahan dan <PERSON>mpatan (KPKT)", "KKR": "Kementerian <PERSON> (KKdW)", "KPDNHEP": "Kementerian Perdagangan Dalam Nege<PERSON> dan <PERSON> (KPDNHEP)", "MOSTI": "Kementerian Sains, Teknologi <PERSON> (MOSTI)", "KPWKM": "Kementerian Pembangunan <PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (KPWKM)", "DBKL": "Dewan Bandaraya Kuala Lumpur (DBKL)", "MBPJ": "<PERSON><PERSON>aling Jaya (MBPJ)", "MBSJ": "<PERSON><PERSON>araya <PERSON>ang <PERSON> (MBSJ)", "MBSA": "<PERSON><PERSON> (MBSA)", "MPK": "<PERSON><PERSON> (MPK)", "MBIP": "<PERSON><PERSON> (MBIP)", "MPJB": "<PERSON><PERSON> (MPJBT)", "MBPP": "<PERSON><PERSON> (MBPP)", "MPSP": "<PERSON><PERSON> (MPSP)", "MBMB": "<PERSON><PERSON> (MBMB)", "MPAG": "<PERSON><PERSON> (MPAG)", "TNB": "Tenaga Nasional Berhad (TNB)", "SYABAS": "Syarikat Bekalan Air Selangor (SYABAS)", "IWK": "Indah Water Konsortium (IWK)", "PLUS": "PLUS Malaysia Berhad", "KTMB": "<PERSON><PERSON><PERSON> (KTMB)", "MRT": "Mass Rapid Transit Corporation (MRT Corp)", "LRT": "Rapid Rail Sdn Bhd", "HSB": "Hospital Selayang", "HKL": "Hospital Kuala Lumpur", "HUSM": "Hospital Universiti Sains Malaysia (HUSM)", "HUKM": "Hospital Universiti Kebangsaan Malaysia (HUKM)", "UM": "Universiti Malaya (UM)", "UKM": "Universiti Kebangsaan Malaysia (UKM)", "USM": "Universiti Sains Malaysia (USM)", "UTM": "Universiti Teknologi Malaysia (UTM)", "UPM": "Universiti Putra Malaysia (UPM)", "UiTM": "Universiti Teknologi MARA (UiTM)"}, "states": {"JH": "<PERSON><PERSON>", "KD": "Kedah", "KT": "<PERSON><PERSON><PERSON>", "ML": "<PERSON><PERSON>", "NS": "<PERSON><PERSON><PERSON>", "PH": "<PERSON><PERSON>", "PN": "<PERSON><PERSON><PERSON>", "PK": "<PERSON><PERSON>", "PL": "<PERSON><PERSON>", "SB": "Sabah", "SW": "Sarawak", "SL": "Selangor", "TR": "Terengganu", "WP": "W.P. Kuala Lumpur", "LBN": "<PERSON><PERSON><PERSON><PERSON>", "PW": "<PERSON><PERSON><PERSON><PERSON>", "OTH": "Other"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password must be at least 8 characters", "passwordMin": "Password must be at least 8 characters", "passwordLowercase": "Password must contain at least one lowercase letter", "passwordUppercase": "Password must contain at least one uppercase letter", "passwordNumber": "Password must contain at least one number", "passwordSpecial": "Password must contain at least one special character (@$!%*?&)", "passwordMatch": "Passwords do not match", "nameMin": "Name must be at least 2 characters", "phoneFormat": "Please enter a valid Malaysian phone number", "roleRequired": "Please select a role", "icNumber": "Please enter a valid IC number"}, "errors": {"somethingWrong": "Something went wrong", "tryAgain": "Please try again", "networkError": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action", "notFound": "The requested resource was not found"}, "error": {"title": "Something went wrong", "description": "We encountered an unexpected error. Please try again or go back to the previous page.", "retryButton": "Try again", "goBackButton": "Go back"}, "notFound": {"title": "404", "description": "Page not found", "backButton": "Back to Home"}, "authPortal": {"title": "Welcome to SimPLE", "subtitle": "Choose your role to access the appropriate portal", "adminPortalTitle": "Administrator <PERSON>", "adminPortalDescription": "For system administrators and JKR personnel", "contractorPortalTitle": "Contractor Portal", "contractorPortalDescription": "For registered contractors and service providers", "adminLogin": "<PERSON><PERSON>", "adminRegister": "Admin Register", "contractorLogin": "Contractor <PERSON><PERSON>", "contractorRegister": "Contractor Register", "helpText": "Need help? Contact support for assistance.", "heroTitle": "Professional Platform", "heroSubtitle": "Connecting administrators and contractors seamlessly"}}